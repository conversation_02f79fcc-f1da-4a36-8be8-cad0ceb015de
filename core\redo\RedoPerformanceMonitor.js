const RedoConfig = require('./RedoConfig');

/**
 * 撤销回退系统性能监控
 */
class RedoPerformanceMonitor {
    
    constructor() {
        this.metrics = new Map(); // constructId -> metrics
        this.globalMetrics = {
            totalOperations: 0,
            totalDiffCalculationTime: 0,
            totalApplyTime: 0,
            totalMemoryUsage: 0,
            averageCompressionRatio: 0,
            errorCount: 0,
            lastCleanupTime: Date.now()
        };
    }

    /**
     * 记录操作开始
     */
    startOperation(constructId, operationType) {
        if (!RedoConfig.get('enablePerformanceMonitoring')) return null;
        
        const operationId = `${constructId}_${operationType}_${Date.now()}_${Math.random()}`;
        const startTime = performance.now();
        
        if (!this.metrics.has(constructId)) {
            this.metrics.set(constructId, {
                operations: [],
                totalTime: 0,
                operationCount: 0,
                memoryUsage: 0,
                lastOptimization: Date.now()
            });
        }

        return {
            operationId,
            constructId,
            operationType,
            startTime,
            startMemory: this.getCurrentMemoryUsage()
        };
    }

    /**
     * 记录操作结束
     */
    endOperation(operationContext, result = {}) {
        if (!operationContext || !RedoConfig.get('enablePerformanceMonitoring')) return;
        
        const endTime = performance.now();
        const duration = endTime - operationContext.startTime;
        const endMemory = this.getCurrentMemoryUsage();
        const memoryDelta = endMemory - operationContext.startMemory;

        const metrics = this.metrics.get(operationContext.constructId);
        if (metrics) {
            metrics.operations.push({
                type: operationContext.operationType,
                duration,
                memoryDelta,
                timestamp: Date.now(),
                result: result
            });
            
            metrics.totalTime += duration;
            metrics.operationCount++;
            metrics.memoryUsage = endMemory;

            // 保持最近100个操作记录
            if (metrics.operations.length > 100) {
                metrics.operations = metrics.operations.slice(-100);
            }
        }

        // 更新全局指标
        this.globalMetrics.totalOperations++;
        
        if (operationContext.operationType === 'diffCalculation') {
            this.globalMetrics.totalDiffCalculationTime += duration;
        } else if (operationContext.operationType === 'applyDiff') {
            this.globalMetrics.totalApplyTime += duration;
        }

        if (result.error) {
            this.globalMetrics.errorCount++;
        }

        // 记录调试信息
        if (RedoConfig.get('enableDebugLogging')) {
            console.log(`撤销操作性能 [${operationContext.operationType}]: ${duration.toFixed(2)}ms, 内存变化: ${(memoryDelta / 1024).toFixed(2)}KB`);
        }

        return {
            duration,
            memoryDelta,
            success: !result.error
        };
    }

    /**
     * 获取当前内存使用量（粗略估算）
     */
    getCurrentMemoryUsage() {
        if (typeof process !== 'undefined' && process.memoryUsage) {
            return process.memoryUsage().heapUsed;
        }
        return 0; // 浏览器环境无法精确获取
    }

    /**
     * 获取项目的性能统计
     */
    getProjectStats(constructId) {
        const metrics = this.metrics.get(constructId);
        if (!metrics) {
            return null;
        }

        const recentOps = metrics.operations.slice(-20); // 最近20个操作
        const avgDuration = recentOps.length > 0 
            ? recentOps.reduce((sum, op) => sum + op.duration, 0) / recentOps.length 
            : 0;

        const operationTypes = {};
        recentOps.forEach(op => {
            operationTypes[op.type] = (operationTypes[op.type] || 0) + 1;
        });

        return {
            totalOperations: metrics.operationCount,
            totalTime: metrics.totalTime,
            averageDuration: metrics.operationCount > 0 ? metrics.totalTime / metrics.operationCount : 0,
            recentAverageDuration: avgDuration,
            currentMemoryUsage: metrics.memoryUsage,
            operationTypes: operationTypes,
            lastOptimization: metrics.lastOptimization
        };
    }

    /**
     * 获取全局性能统计
     */
    getGlobalStats() {
        const avgDiffTime = this.globalMetrics.totalOperations > 0 
            ? this.globalMetrics.totalDiffCalculationTime / this.globalMetrics.totalOperations 
            : 0;
        
        const avgApplyTime = this.globalMetrics.totalOperations > 0 
            ? this.globalMetrics.totalApplyTime / this.globalMetrics.totalOperations 
            : 0;

        return {
            ...this.globalMetrics,
            averageDiffCalculationTime: avgDiffTime,
            averageApplyTime: avgApplyTime,
            errorRate: this.globalMetrics.totalOperations > 0 
                ? (this.globalMetrics.errorCount / this.globalMetrics.totalOperations * 100).toFixed(2) + '%'
                : '0%'
        };
    }

    /**
     * 检查是否需要性能优化
     */
    shouldOptimize(constructId) {
        const metrics = this.metrics.get(constructId);
        if (!metrics) return false;

        const config = RedoConfig.getAll();
        const timeSinceLastOptimization = Date.now() - metrics.lastOptimization;
        const operationsSinceOptimization = metrics.operations.filter(
            op => op.timestamp > metrics.lastOptimization
        ).length;

        // 检查优化条件
        const conditions = [
            operationsSinceOptimization >= config.optimizationInterval,
            timeSinceLastOptimization > 300000, // 5分钟
            metrics.memoryUsage > config.memoryWarningThreshold
        ];

        return conditions.some(condition => condition);
    }

    /**
     * 记录优化操作
     */
    recordOptimization(constructId, optimizationResult) {
        const metrics = this.metrics.get(constructId);
        if (metrics) {
            metrics.lastOptimization = Date.now();
        }

        if (RedoConfig.get('enableDebugLogging')) {
            console.log(`撤销队列优化完成 [${constructId}]:`, optimizationResult);
        }
    }

    /**
     * 生成性能报告
     */
    generateReport(constructId = null) {
        const report = {
            timestamp: new Date().toISOString(),
            globalStats: this.getGlobalStats(),
            config: RedoConfig.getAll()
        };

        if (constructId) {
            report.projectStats = this.getProjectStats(constructId);
        } else {
            report.allProjects = {};
            for (const [id, _] of this.metrics) {
                report.allProjects[id] = this.getProjectStats(id);
            }
        }

        return report;
    }

    /**
     * 清理过期的性能数据
     */
    cleanup() {
        const maxAge = 24 * 60 * 60 * 1000; // 24小时
        const cutoffTime = Date.now() - maxAge;

        for (const [constructId, metrics] of this.metrics) {
            metrics.operations = metrics.operations.filter(
                op => op.timestamp > cutoffTime
            );

            // 如果没有最近的操作记录，删除整个项目的指标
            if (metrics.operations.length === 0) {
                this.metrics.delete(constructId);
            }
        }

        this.globalMetrics.lastCleanupTime = Date.now();
        
        if (RedoConfig.get('enableDebugLogging')) {
            console.log('性能监控数据清理完成');
        }
    }

    /**
     * 获取性能警告
     */
    getPerformanceWarnings(constructId = null) {
        const warnings = [];
        const config = RedoConfig.getAll();

        if (constructId) {
            const stats = this.getProjectStats(constructId);
            if (stats) {
                if (stats.recentAverageDuration > 100) {
                    warnings.push({
                        type: 'performance',
                        project: constructId,
                        message: `撤销操作平均耗时过长: ${stats.recentAverageDuration.toFixed(2)}ms`
                    });
                }

                if (stats.currentMemoryUsage > config.memoryWarningThreshold) {
                    warnings.push({
                        type: 'memory',
                        project: constructId,
                        message: `内存使用过高: ${(stats.currentMemoryUsage / 1024 / 1024).toFixed(2)}MB`
                    });
                }
            }
        } else {
            // 检查全局警告
            const globalStats = this.getGlobalStats();
            if (globalStats.errorRate > '5%') {
                warnings.push({
                    type: 'error',
                    message: `撤销操作错误率过高: ${globalStats.errorRate}`
                });
            }
        }

        return warnings;
    }

    /**
     * 重置所有性能数据
     */
    reset() {
        this.metrics.clear();
        this.globalMetrics = {
            totalOperations: 0,
            totalDiffCalculationTime: 0,
            totalApplyTime: 0,
            totalMemoryUsage: 0,
            averageCompressionRatio: 0,
            errorCount: 0,
            lastCleanupTime: Date.now()
        };
    }
}

// 单例模式
const performanceMonitor = new RedoPerformanceMonitor();

module.exports = performanceMonitor;
