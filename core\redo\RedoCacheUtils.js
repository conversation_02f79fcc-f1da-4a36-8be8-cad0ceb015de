
const {ObjectUtil} = require('../../common/ObjectUtil');
const DynamicQueue = require('./DynamicQueue');
const ObjectCacheUtils = require('./ObjectCacheUtils');
const DiffUtils = require('./DiffUtils');
const RedoConfig = require('./RedoConfig');
const performanceMonitor = require('./RedoPerformanceMonitor');
//const {CryptoUtils} = require("../../electron/utils/CrypUtils");
//const {toJsonYsfString} = require("../../electron/main_editor/util");
const os = require('os');
//const NumberUtil = require('../../electron/utils/NumberUtil');

class RedoCacheUtils{

    constructor(){
        ObjectCacheUtils.init();
    }
    
    addRedoCache(params,channel){
        // 开始性能监控
        const perfContext = performanceMonitor.startOperation(params['constructId'], 'addRedoCache');

        try {
            // 获取内存对象
            let realObject = ObjectCacheUtils.get(params['constructId'],channel);
            if(ObjectUtil.isEmpty(realObject)){
                return;
            }

            let queue = this.getRedoQueue(params['constructId']);

            // 检查是否启用差异模式
            if (!RedoConfig.get('enableDiffMode')) {
                // 使用传统的完整对象备份模式
                return this.addRedoCacheLegacy(params, channel, queue, realObject);
            }

            // 差异计算性能监控
            const diffContext = performanceMonitor.startOperation(params['constructId'], 'diffCalculation');

            // 获取上一个状态用于计算差异
            let lastObject = queue.getLastItem();
            let previousState = null;

            if(ObjectUtil.isNotEmpty(lastObject)){
                // 如果有上一个状态，使用其完整对象作为比较基准
                previousState = lastObject.fullObject || lastObject.item;
            } else {
                // 如果没有上一个状态，创建一个空对象作为基准
                previousState = this.createEmptyState(realObject);
            }

            // 计算当前状态与上一状态的差异
            let diff = DiffUtils.calculateDiff(previousState, realObject);

            // 压缩差异以减少存储空间
            let compressedDiff = DiffUtils.compressDiff(diff);

            // 结束差异计算监控
            performanceMonitor.endOperation(diffContext, {
                diffSize: compressedDiff ? JSON.stringify(compressedDiff).length : 0
            });

            // 获取差异统计信息用于监控
            let diffStats = DiffUtils.getDiffStats(compressedDiff);

            if (RedoConfig.get('enableDebugLogging')) {
                console.log(`撤销记录差异统计 - 变更数量: ${diffStats.changeCount}, 大小: ${diffStats.size} bytes`);
            }

            // 创建当前状态的完整拷贝（仅在必要时）
            let fullObject = null;
            let shouldStoreFullObject = this.shouldStoreFullObject(queue, diffStats);

            if (shouldStoreFullObject) {
                fullObject = ObjectUtil.cloneDeep(realObject);
                if (RedoConfig.get('enableDebugLogging')) {
                    console.log(`存储完整对象快照，队列长度: ${queue.queue.length}`);
                }
            }

            // 设置上一个对象的恢复数据
            if(ObjectUtil.isNotEmpty(lastObject) && !lastObject.redoItem){
                lastObject.redoItem = ObjectUtil.cloneDeep(realObject);//用于恢复
            }

            // 入队新的差异记录
            queue.enqueue({
                sequenceNbr: params.sequenceNbr,
                item: compressedDiff, // 存储差异而不是完整对象
                fullObject: fullObject, // 可选的完整对象快照
                previousState: previousState, // 用于应用差异的基准状态
                channel: channel,
                name: params.name,
                params: params,
                isDiff: true, // 标记这是差异记录
                diffStats: diffStats,
                timestamp: Date.now()
            });

            // 检查是否需要优化
            if (RedoConfig.get('enableAutoOptimization') &&
                performanceMonitor.shouldOptimize(params['constructId'])) {
                this.performOptimization(params['constructId']);
            }

            // 结束性能监控
            performanceMonitor.endOperation(perfContext, {
                success: true,
                queueLength: queue.queue.length,
                diffStats: diffStats
            });

        } catch (error) {
            console.error('添加撤销缓存时发生错误:', error);
            performanceMonitor.endOperation(perfContext, { error: error.message });
            throw error;
        }
    }

    /**
     * 传统的完整对象备份模式（向后兼容）
     */
    addRedoCacheLegacy(params, channel, queue, realObject) {
        let copyObject = ObjectUtil.cloneDeep(realObject);
        let lastObject = queue.getLastItem();
        if(ObjectUtil.isNotEmpty(lastObject)){
            lastObject.redoItem = copyObject;//用于恢复
        }
        queue.enqueue({
            sequenceNbr: params.sequenceNbr,
            item: copyObject,
            channel: channel,
            name: params.name,
            params: params,
            isDiff: false, // 标记这不是差异记录
            timestamp: Date.now()
        });
    }

    /**
     * 执行队列优化
     */
    performOptimization(constructId) {
        try {
            const optimizationContext = performanceMonitor.startOperation(constructId, 'optimization');

            // 清理过期记录
            const removedCount = this.cleanupOldRecords(constructId, RedoConfig.get('maxRecordAge'));

            // 优化队列
            const optimizedCount = this.optimizeQueue(constructId);

            const result = {
                removedExpired: removedCount,
                optimizedItems: optimizedCount,
                timestamp: Date.now()
            };

            performanceMonitor.recordOptimization(constructId, result);
            performanceMonitor.endOperation(optimizationContext, { success: true, result });

        } catch (error) {
            console.error('队列优化失败:', error);
        }
    }

    /**
     * 创建空状态对象作为差异计算的基准
     */
    createEmptyState(realObject) {
        // 根据对象类型创建相应的空状态
        if (Array.isArray(realObject)) {
            return [];
        } else if (typeof realObject === 'object' && realObject !== null) {
            return {};
        }
        return null;
    }

    /**
     * 判断是否应该存储完整对象快照
     * 策略：根据配置的间隔或差异过大时存储完整快照
     */
    shouldStoreFullObject(queue, diffStats) {
        const queueLength = queue.queue.length;
        const fullSnapshotInterval = RedoConfig.get('fullSnapshotInterval');
        const maxDiffSize = RedoConfig.get('maxDiffSize');

        return (queueLength % fullSnapshotInterval === 0) ||
               (diffStats.size > maxDiffSize);
    }

    /**
     * 从差异记录重建完整对象
     */
    reconstructObjectFromDiff(queueObject) {
        if (!queueObject.isDiff) {
            // 如果不是差异记录，直接返回原对象
            return queueObject.item;
        }

        // 如果有完整对象快照，直接使用
        if (queueObject.fullObject) {
            return queueObject.fullObject;
        }

        // 从基准状态应用差异
        if (queueObject.previousState && queueObject.item) {
            return DiffUtils.applyDiff(queueObject.previousState, queueObject.item);
        }

        console.warn('无法重建对象：缺少必要的状态信息');
        return null;
    }

    /**
     * 获取指定时间点的完整对象状态
     */
    getObjectStateAtPoint(constructId, targetSequenceNbr) {
        let queue = this.getRedoQueue(constructId);
        let targetIndex = queue.queue.findIndex(item => item.sequenceNbr === targetSequenceNbr);

        if (targetIndex === -1) {
            return null;
        }

        // 从最近的完整快照开始重建
        let baseIndex = targetIndex;
        let baseObject = null;

        // 向前查找最近的完整快照
        for (let i = targetIndex; i >= 0; i--) {
            if (queue.queue[i].fullObject) {
                baseIndex = i;
                baseObject = ObjectUtil.cloneDeep(queue.queue[i].fullObject);
                break;
            }
        }

        // 如果没有找到完整快照，从空状态开始
        if (!baseObject) {
            baseObject = this.createEmptyState(ObjectCacheUtils.get(constructId, queue.queue[0].channel));
            baseIndex = -1;
        }

        // 应用从基准到目标的所有差异
        for (let i = baseIndex + 1; i <= targetIndex; i++) {
            let queueItem = queue.queue[i];
            if (queueItem.isDiff && queueItem.item) {
                baseObject = DiffUtils.applyDiff(baseObject, queueItem.item);
            }
        }

        return baseObject;
    }

    /**
     * 获取撤销队列的内存使用统计
     */
    getMemoryStats(constructId) {
        let queue = this.getRedoQueue(constructId);
        let stats = {
            totalItems: queue.queue.length,
            diffItems: 0,
            fullItems: 0,
            totalSize: 0,
            diffSize: 0,
            fullSize: 0,
            compressionRatio: 0
        };

        queue.queue.forEach(item => {
            let itemSize = this.calculateObjectSize(item);
            stats.totalSize += itemSize;

            if (item.isDiff) {
                stats.diffItems++;
                stats.diffSize += itemSize;
            } else {
                stats.fullItems++;
                stats.fullSize += itemSize;
            }
        });

        if (stats.fullSize > 0) {
            stats.compressionRatio = ((stats.fullSize - stats.diffSize) / stats.fullSize * 100).toFixed(2);
        }

        return stats;
    }

    /**
     * 计算对象的大致内存大小（字节）
     */
    calculateObjectSize(obj) {
        try {
            return JSON.stringify(obj).length * 2; // 粗略估算，每个字符2字节
        } catch (e) {
            return 0;
        }
    }

    /**
     * 清理过期的撤销记录以释放内存
     */
    cleanupOldRecords(constructId, maxAge = 3600000) { // 默认1小时
        let queue = this.getRedoQueue(constructId);
        let currentTime = Date.now();
        let originalLength = queue.queue.length;

        queue.queue = queue.queue.filter(item => {
            return !item.timestamp || (currentTime - item.timestamp) < maxAge;
        });

        let removedCount = originalLength - queue.queue.length;
        if (removedCount > 0) {
            console.log(`清理了 ${removedCount} 条过期的撤销记录`);
            // 重新调整指针
            if (queue.currentPointer >= queue.queue.length) {
                queue.currentPointer = queue.queue.length - 1;
            }
        }

        return removedCount;
    }

    /**
     * 优化撤销队列，合并连续的小差异
     */
    optimizeQueue(constructId) {
        let queue = this.getRedoQueue(constructId);
        let optimizedQueue = [];
        let pendingDiffs = [];

        for (let i = 0; i < queue.queue.length; i++) {
            let item = queue.queue[i];

            if (item.isDiff && item.diffStats && item.diffStats.changeCount < 5) {
                // 收集小的差异记录
                pendingDiffs.push(item);
            } else {
                // 如果有待处理的小差异，尝试合并
                if (pendingDiffs.length > 1) {
                    let mergedItem = this.mergeDiffItems(pendingDiffs);
                    if (mergedItem) {
                        optimizedQueue.push(mergedItem);
                    } else {
                        optimizedQueue.push(...pendingDiffs);
                    }
                } else if (pendingDiffs.length === 1) {
                    optimizedQueue.push(pendingDiffs[0]);
                }

                pendingDiffs = [];
                optimizedQueue.push(item);
            }
        }

        // 处理剩余的待合并项
        if (pendingDiffs.length > 0) {
            optimizedQueue.push(...pendingDiffs);
        }

        let originalLength = queue.queue.length;
        queue.queue = optimizedQueue;

        console.log(`队列优化完成：${originalLength} -> ${optimizedQueue.length} 项`);
        return originalLength - optimizedQueue.length;
    }

    /**
     * 合并多个差异项（简单实现）
     */
    mergeDiffItems(diffItems) {
        if (diffItems.length < 2) return null;

        // 简单合并策略：使用第一个和最后一个的差异
        let firstItem = diffItems[0];
        let lastItem = diffItems[diffItems.length - 1];

        return {
            sequenceNbr: `merged_${firstItem.sequenceNbr}_${lastItem.sequenceNbr}`,
            item: lastItem.item, // 使用最后的差异
            channel: firstItem.channel,
            name: `合并操作 (${diffItems.length}项)`,
            params: lastItem.params,
            isDiff: true,
            diffStats: {
                changeCount: diffItems.reduce((sum, item) => sum + (item.diffStats?.changeCount || 0), 0),
                size: diffItems.reduce((sum, item) => sum + (item.diffStats?.size || 0), 0)
            },
            timestamp: lastItem.timestamp,
            mergedFrom: diffItems.map(item => item.sequenceNbr)
        };
    }

    /**
     * 获取撤销系统的完整状态报告
     */
    getSystemReport(constructId = null) {
        const report = {
            timestamp: new Date().toISOString(),
            config: RedoConfig.getAll(),
            performance: performanceMonitor.generateReport(constructId)
        };

        if (constructId) {
            const memoryStats = this.getMemoryStats(constructId);
            const warnings = performanceMonitor.getPerformanceWarnings(constructId);
            const recommendations = RedoConfig.getPerformanceRecommendations(memoryStats);

            report.project = {
                constructId,
                memoryStats,
                warnings,
                recommendations
            };
        } else {
            // 全局报告
            report.global = {
                totalProjects: this.redoQueueMap.size,
                warnings: performanceMonitor.getPerformanceWarnings(),
                totalMemoryUsage: this.getTotalMemoryUsage()
            };
        }

        return report;
    }

    /**
     * 获取所有项目的总内存使用量
     */
    getTotalMemoryUsage() {
        let totalSize = 0;
        for (const [constructId, _] of this.redoQueueMap) {
            const stats = this.getMemoryStats(constructId);
            totalSize += stats.totalSize;
        }
        return totalSize;
    }

    /**
     * 强制优化指定项目的撤销队列
     */
    forceOptimize(constructId) {
        return this.performOptimization(constructId);
    }

    /**
     * 设置撤销系统配置
     */
    setConfig(newConfig) {
        RedoConfig.setConfig(newConfig);

        if (RedoConfig.get('enableDebugLogging')) {
            console.log('撤销系统配置已更新:', newConfig);
        }
    }

    /**
     * 获取撤销系统配置
     */
    getConfig() {
        return RedoConfig.getAll();
    }

    /**
     * 重置性能监控数据
     */
    resetPerformanceData() {
        performanceMonitor.reset();
    }

    /**
     * 导出撤销数据（用于调试）
     */
    exportDebugData(constructId) {
        const queue = this.getRedoQueue(constructId);
        return {
            constructId,
            queueLength: queue.queue.length,
            currentPointer: queue.currentPointer,
            items: queue.queue.map(item => ({
                sequenceNbr: item.sequenceNbr,
                name: item.name,
                isDiff: item.isDiff,
                diffStats: item.diffStats,
                timestamp: item.timestamp,
                hasFullObject: !!item.fullObject
            })),
            memoryStats: this.getMemoryStats(constructId),
            performanceStats: performanceMonitor.getProjectStats(constructId)
        };
    }
    getRedoQueue(constructId) {
        if (!global.redoMap) {
            global.redoMap = new Map();
        }
        let queue =  global.redoMap.get(constructId);
        if(queue == null || queue == undefined){
            queue = this.createRedoQueue(constructId);
        }
        return queue;
    }

    createRedoQueue(constructId){
        if (!global.redoMap) {
            global.redoMap = new Map();
        }

        let initSize = 20;

        const freeMemory = os.freemem();
        let resultInt = freeMemory/1024/1024/100;
        
        if(resultInt <= 20){//2g
            initSize = 10;
        }
        if(resultInt <= 10){//1g
            initSize = 5;
        }
        //先固定大小 默认20
        let queue = new DynamicQueue(initSize);

        global.redoMap.set(constructId, queue);
        return queue;
    }
    /**
     * 恢复
     * @param {*} constructId
     * @param {*} queueId
     * @returns
     */
    redo(constructId,queueId){
        let queue = this.getRedoQueue(constructId);
        let queueObject = null;
        if(ObjectUtil.isEmpty(queueId)){
            queueObject = queue.dequeue(1);
        }else{
            queueObject = queue.dequeueById(queueId,1);
        }
        if(ObjectUtil.isNotEmpty(queueObject)){
            let objectToRestore = null;

            if (queueObject.isDiff) {
                // 如果是差异记录，重建完整对象
                objectToRestore = this.reconstructObjectFromDiff(queueObject);
                if (!objectToRestore) {
                    // 如果重建失败，尝试使用redoItem
                    objectToRestore = queueObject.redoItem;
                }
            } else {
                // 传统的完整对象记录
                objectToRestore = queueObject.redoItem;
            }

            if (objectToRestore) {
                ObjectCacheUtils.updateObject(constructId, objectToRestore, queueObject.channel);
                console.log(`恢复操作完成 - ${queueObject.name}, 差异记录: ${queueObject.isDiff || false}`);
            } else {
                console.error('恢复失败：无法获取要恢复的对象状态');
                return {};
            }
        }else{
            return {};
        }
        return {sequenceNbr:queueObject.sequenceNbr,
            channel:queueObject.channel,
            name:queueObject.name,
            params: queueObject.params
        };
    }

    /**
     * 撤销
     * @param {*} constructId
     * @param {*} queueId
     * @returns
     */
    undo(constructId,queueId){
        let queue = this.getRedoQueue(constructId);
        let queueObject = null;
        if(ObjectUtil.isEmpty(queueId)){
            queueObject = queue.dequeue(0);
        }else{
            queueObject = queue.dequeueById(queueId,0);
        }
        if(ObjectUtil.isNotEmpty(queueObject)){
            let lastQueue = queue.getLastItem();//撤销时判断当前最新的一条是否保存了当前对象，用于恢复
            if(ObjectUtil.isEmpty(lastQueue.redoItem)){
                lastQueue.redoItem = ObjectCacheUtils.get(constructId,queueObject.channel);
            }

            let objectToRestore = null;

            if (queueObject.isDiff) {
                // 如果是差异记录，需要反向应用差异
                let currentObject = ObjectCacheUtils.get(constructId, queueObject.channel);
                if (queueObject.item && currentObject) {
                    objectToRestore = DiffUtils.reverseDiff(currentObject, queueObject.item);
                } else if (queueObject.previousState) {
                    // 如果没有当前对象，使用之前的状态
                    objectToRestore = queueObject.previousState;
                }
                console.log(`撤销差异记录 - ${queueObject.name}`);
            } else {
                // 传统的完整对象记录
                objectToRestore = queueObject.item;
                console.log(`撤销完整对象记录 - ${queueObject.name}`);
            }

            if (objectToRestore) {
                ObjectCacheUtils.updateObject(constructId, objectToRestore, queueObject.channel);
            } else {
                console.error('撤销失败：无法获取要恢复的对象状态');
                return {};
            }
        }else{
            return {};
        }
        return {sequenceNbr:queueObject.sequenceNbr,
            channel:queueObject.channel,
            name:queueObject.name,
            params: queueObject.params
        };
    }
    
    clearRedoList(params){
        if (!global.redoMap) {
            global.redoMap = new Map();
        }
        global.redoMap.delete(params.constructId);
    }
}

module.exports = RedoCacheUtils
  