
const {ObjectUtil} = require('../../common/ObjectUtil');
const DynamicQueue = require('./DynamicQueue');
const ObjectCacheUtils = require('./ObjectCacheUtils');
const DiffUtils = require('./DiffUtils');
const RedoConfig = require('./RedoConfig');
const performanceMonitor = require('./RedoPerformanceMonitor');
//const {CryptoUtils} = require("../../electron/utils/CrypUtils");
//const {toJsonYsfString} = require("../../electron/main_editor/util");
const os = require('os');
//const NumberUtil = require('../../electron/utils/NumberUtil');

class RedoCacheUtils{

    constructor(){
        ObjectCacheUtils.init();
    }
    
    addRedoCache(params,channel){
        // 开始性能监控
        const perfContext = performanceMonitor.startOperation(params['constructId'], 'addRedoCache');

        try {
            // 获取内存对象
            let realObject = ObjectCacheUtils.get(params['constructId'],channel);
            if(ObjectUtil.isEmpty(realObject)){
                return;
            }

            let queue = this.getRedoQueue(params['constructId']);

            // 检查是否启用差异模式
            if (!RedoConfig.get('enableDiffMode')) {
                // 使用传统的完整对象备份模式
                return this.addRedoCacheLegacy(params, channel, queue, realObject);
            }

            // 差异计算性能监控
            const diffContext = performanceMonitor.startOperation(params['constructId'], 'diffCalculation');

            // 重要修复：我们应该保存操作前的状态，而不是计算差异
            // 获取当前内存中的对象状态（这是操作前的状态）
            let currentMemoryObject = ObjectCacheUtils.get(params['constructId'], channel);
            let previousState = null;

            try {
                // 安全克隆当前状态作为操作前的快照
                previousState = this.safeCloneObject(currentMemoryObject);
            } catch (cloneError) {
                console.warn('克隆当前状态失败，回退到传统模式:', cloneError.message);
                performanceMonitor.endOperation(diffContext, { error: cloneError.message });
                return this.addRedoCacheLegacy(params, channel, queue, realObject);
            }

            // 计算操作前状态与操作后状态的差异
            let diff = null;
            let compressedDiff = null;

            try {
                // 这里 previousState 是操作前的状态，realObject 是操作后的状态
                diff = DiffUtils.calculateDiff(previousState, realObject);
                // 压缩差异以减少存储空间
                compressedDiff = DiffUtils.compressDiff(diff);
            } catch (diffError) {
                console.warn('差异计算失败，回退到传统模式:', diffError.message);
                // 如果差异计算失败，回退到传统的完整对象备份
                performanceMonitor.endOperation(diffContext, { error: diffError.message });
                return this.addRedoCacheLegacy(params, channel, queue, realObject);
            }

            // 结束差异计算监控
            performanceMonitor.endOperation(diffContext, {
                diffSize: compressedDiff ? DiffUtils.safeStringify(compressedDiff).length : 0
            });

            // 获取差异统计信息用于监控
            let diffStats = DiffUtils.getDiffStats(compressedDiff);

            if (RedoConfig.get('enableDebugLogging')) {
                console.log(`撤销记录差异统计 - 变更数量: ${diffStats.changeCount}, 大小: ${diffStats.size} bytes`);
            }

            // 创建当前状态的完整拷贝（仅在必要时）
            let fullObject = null;
            let shouldStoreFullObject = this.shouldStoreFullObject(queue, diffStats);

            if (shouldStoreFullObject) {
                fullObject = ObjectUtil.cloneDeep(realObject);
                if (RedoConfig.get('enableDebugLogging')) {
                    console.log(`存储完整对象快照，队列长度: ${queue.queue.length}`);
                }
            }

            // 设置上一个对象的恢复数据（用于传统模式兼容）
            let lastObject = queue.getLastItem();
            if(ObjectUtil.isNotEmpty(lastObject) && !lastObject.redoItem){
                if (lastObject.isDiff) {
                    // 对于差异记录，redoItem 应该是操作后的状态
                    lastObject.redoItem = this.safeCloneObject(realObject);
                } else {
                    // 对于传统记录，保持原有逻辑
                    lastObject.redoItem = this.safeCloneObject(realObject);
                }
            }

            // 入队新的差异记录
            queue.enqueue({
                sequenceNbr: params.sequenceNbr,
                item: previousState, // 存储操作前的状态（用于撤销）
                redoState: this.safeCloneObject(realObject), // 存储操作后的状态（用于重做）
                diff: compressedDiff, // 存储差异（用于分析和优化）
                fullObject: fullObject, // 可选的完整对象快照
                channel: channel,
                name: params.name,
                params: params,
                isDiff: true, // 标记这是差异记录
                diffStats: diffStats,
                timestamp: Date.now()
            });

            // 检查是否需要优化
            if (RedoConfig.get('enableAutoOptimization') &&
                performanceMonitor.shouldOptimize(params['constructId'])) {
                this.performOptimization(params['constructId']);
            }

            // 结束性能监控
            performanceMonitor.endOperation(perfContext, {
                success: true,
                queueLength: queue.queue.length,
                diffStats: diffStats
            });

        } catch (error) {
            console.error('添加撤销缓存时发生错误:', error);
            performanceMonitor.endOperation(perfContext, { error: error.message });
            throw error;
        }
    }

    /**
     * 传统的完整对象备份模式（向后兼容）
     */
    addRedoCacheLegacy(params, channel, queue, realObject) {
        try {
            let copyObject = this.safeCloneObject(realObject);
            let lastObject = queue.getLastItem();
            if(ObjectUtil.isNotEmpty(lastObject)){
                lastObject.redoItem = copyObject;//用于恢复
            }
            queue.enqueue({
                sequenceNbr: params.sequenceNbr,
                item: copyObject,
                channel: channel,
                name: params.name,
                params: params,
                isDiff: false, // 标记这不是差异记录
                timestamp: Date.now(),
                fallbackMode: true // 标记这是回退模式
            });

            if (RedoConfig.get('enableDebugLogging')) {
                console.log(`使用传统备份模式: ${params.name}`);
            }
        } catch (error) {
            console.error('传统备份模式也失败了:', error.message);
            // 最后的回退：只保存关键信息
            this.addMinimalRedoCache(params, channel, queue);
        }
    }

    /**
     * 安全的对象克隆，处理循环引用
     */
    safeCloneObject(obj) {
        if (!RedoConfig.get('handleCircularReferences')) {
            return ObjectUtil.cloneDeep(obj);
        }

        try {
            // 尝试使用标准克隆
            return ObjectUtil.cloneDeep(obj);
        } catch (error) {
            if (error.message.includes('circular') || error.message.includes('Converting circular structure')) {
                // 如果遇到循环引用，使用安全克隆
                return this.cloneWithoutCircularRefs(obj);
            }
            throw error;
        }
    }

    /**
     * 克隆对象但排除循环引用
     */
    cloneWithoutCircularRefs(obj, visited = new WeakSet()) {
        if (obj === null || typeof obj !== 'object') {
            return obj;
        }

        if (visited.has(obj)) {
            return { __circularRef: true, type: obj.constructor?.name || 'Object' };
        }

        visited.add(obj);

        if (Array.isArray(obj)) {
            const result = obj.slice(0, 100).map(item => this.cloneWithoutCircularRefs(item, visited));
            visited.delete(obj);
            return result;
        }

        const result = {};
        const excludeFields = ['parent', 'children', '__proto__', 'constructor'];

        for (const [key, value] of Object.entries(obj)) {
            if (excludeFields.includes(key) || typeof value === 'function') {
                continue;
            }

            try {
                result[key] = this.cloneWithoutCircularRefs(value, visited);
            } catch (error) {
                result[key] = { __error: 'Failed to clone', type: typeof value };
            }
        }

        visited.delete(obj);
        return result;
    }

    /**
     * 最小化的撤销缓存，只保存关键信息
     */
    addMinimalRedoCache(params, channel, queue) {
        queue.enqueue({
            sequenceNbr: params.sequenceNbr,
            item: null, // 不保存对象内容
            channel: channel,
            name: params.name,
            params: params,
            isDiff: false,
            isMinimal: true, // 标记这是最小化记录
            timestamp: Date.now(),
            error: 'Failed to create backup due to circular references'
        });

        console.warn(`创建最小化撤销记录: ${params.name} (由于循环引用问题)`);
    }

    /**
     * 执行队列优化
     */
    performOptimization(constructId) {
        try {
            const optimizationContext = performanceMonitor.startOperation(constructId, 'optimization');

            // 清理过期记录
            const removedCount = this.cleanupOldRecords(constructId, RedoConfig.get('maxRecordAge'));

            // 优化队列
            const optimizedCount = this.optimizeQueue(constructId);

            const result = {
                removedExpired: removedCount,
                optimizedItems: optimizedCount,
                timestamp: Date.now()
            };

            performanceMonitor.recordOptimization(constructId, result);
            performanceMonitor.endOperation(optimizationContext, { success: true, result });

        } catch (error) {
            console.error('队列优化失败:', error);
        }
    }

    /**
     * 创建空状态对象作为差异计算的基准
     */
    createEmptyState(realObject) {
        // 根据对象类型创建相应的空状态
        if (Array.isArray(realObject)) {
            return [];
        } else if (typeof realObject === 'object' && realObject !== null) {
            return {};
        }
        return null;
    }

    /**
     * 判断是否应该存储完整对象快照
     * 策略：根据配置的间隔或差异过大时存储完整快照
     */
    shouldStoreFullObject(queue, diffStats) {
        const queueLength = queue.queue.length;
        const fullSnapshotInterval = RedoConfig.get('fullSnapshotInterval');
        const maxDiffSize = RedoConfig.get('maxDiffSize');

        return (queueLength % fullSnapshotInterval === 0) ||
               (diffStats.size > maxDiffSize);
    }

    /**
     * 从差异记录重建完整对象（现在主要用于获取重做状态）
     */
    reconstructObjectFromDiff(queueObject) {
        if (!queueObject.isDiff) {
            // 如果不是差异记录，直接返回原对象
            return queueObject.item;
        }

        // 如果有存储的重做状态，直接使用
        if (queueObject.redoState) {
            return queueObject.redoState;
        }

        // 如果有完整对象快照，直接使用
        if (queueObject.fullObject) {
            return queueObject.fullObject;
        }

        // 如果有差异信息，从操作前状态应用差异得到操作后状态
        if (queueObject.item && queueObject.diff) {
            try {
                return DiffUtils.applyDiff(queueObject.item, queueObject.diff);
            } catch (error) {
                console.warn('应用差异失败:', error.message);
            }
        }

        console.warn('无法重建对象：缺少必要的状态信息');
        return null;
    }

    /**
     * 获取指定时间点的完整对象状态
     */
    getObjectStateAtPoint(constructId, targetSequenceNbr) {
        let queue = this.getRedoQueue(constructId);
        let targetIndex = queue.queue.findIndex(item => item.sequenceNbr === targetSequenceNbr);

        if (targetIndex === -1) {
            return null;
        }

        // 从最近的完整快照开始重建
        let baseIndex = targetIndex;
        let baseObject = null;

        // 向前查找最近的完整快照
        for (let i = targetIndex; i >= 0; i--) {
            if (queue.queue[i].fullObject) {
                baseIndex = i;
                baseObject = ObjectUtil.cloneDeep(queue.queue[i].fullObject);
                break;
            }
        }

        // 如果没有找到完整快照，从空状态开始
        if (!baseObject) {
            baseObject = this.createEmptyState(ObjectCacheUtils.get(constructId, queue.queue[0].channel));
            baseIndex = -1;
        }

        // 应用从基准到目标的所有差异
        for (let i = baseIndex + 1; i <= targetIndex; i++) {
            let queueItem = queue.queue[i];
            if (queueItem.isDiff && queueItem.item) {
                baseObject = DiffUtils.applyDiff(baseObject, queueItem.item);
            }
        }

        return baseObject;
    }

    /**
     * 获取撤销队列的内存使用统计
     */
    getMemoryStats(constructId) {
        let queue = this.getRedoQueue(constructId);
        let stats = {
            totalItems: queue.queue.length,
            diffItems: 0,
            fullItems: 0,
            totalSize: 0,
            diffSize: 0,
            fullSize: 0,
            compressionRatio: 0
        };

        queue.queue.forEach(item => {
            let itemSize = this.calculateObjectSize(item);
            stats.totalSize += itemSize;

            if (item.isDiff) {
                stats.diffItems++;
                stats.diffSize += itemSize;
            } else {
                stats.fullItems++;
                stats.fullSize += itemSize;
            }
        });

        if (stats.fullSize > 0) {
            stats.compressionRatio = ((stats.fullSize - stats.diffSize) / stats.fullSize * 100).toFixed(2);
        }

        return stats;
    }

    /**
     * 计算对象的大致内存大小（字节）
     */
    calculateObjectSize(obj) {
        try {
            return DiffUtils.safeStringify(obj).length * 2; // 粗略估算，每个字符2字节
        } catch (e) {
            console.warn('计算对象大小时出错:', e.message);
            return 1000; // 返回一个估算值
        }
    }

    /**
     * 清理过期的撤销记录以释放内存
     */
    cleanupOldRecords(constructId, maxAge = 3600000) { // 默认1小时
        let queue = this.getRedoQueue(constructId);
        let currentTime = Date.now();
        let originalLength = queue.queue.length;

        queue.queue = queue.queue.filter(item => {
            return !item.timestamp || (currentTime - item.timestamp) < maxAge;
        });

        let removedCount = originalLength - queue.queue.length;
        if (removedCount > 0) {
            console.log(`清理了 ${removedCount} 条过期的撤销记录`);
            // 重新调整指针
            if (queue.currentPointer >= queue.queue.length) {
                queue.currentPointer = queue.queue.length - 1;
            }
        }

        return removedCount;
    }

    /**
     * 优化撤销队列，合并连续的小差异
     */
    optimizeQueue(constructId) {
        let queue = this.getRedoQueue(constructId);
        let optimizedQueue = [];
        let pendingDiffs = [];

        for (let i = 0; i < queue.queue.length; i++) {
            let item = queue.queue[i];

            if (item.isDiff && item.diffStats && item.diffStats.changeCount < 5) {
                // 收集小的差异记录
                pendingDiffs.push(item);
            } else {
                // 如果有待处理的小差异，尝试合并
                if (pendingDiffs.length > 1) {
                    let mergedItem = this.mergeDiffItems(pendingDiffs);
                    if (mergedItem) {
                        optimizedQueue.push(mergedItem);
                    } else {
                        optimizedQueue.push(...pendingDiffs);
                    }
                } else if (pendingDiffs.length === 1) {
                    optimizedQueue.push(pendingDiffs[0]);
                }

                pendingDiffs = [];
                optimizedQueue.push(item);
            }
        }

        // 处理剩余的待合并项
        if (pendingDiffs.length > 0) {
            optimizedQueue.push(...pendingDiffs);
        }

        let originalLength = queue.queue.length;
        queue.queue = optimizedQueue;

        console.log(`队列优化完成：${originalLength} -> ${optimizedQueue.length} 项`);
        return originalLength - optimizedQueue.length;
    }

    /**
     * 合并多个差异项（简单实现）
     */
    mergeDiffItems(diffItems) {
        if (diffItems.length < 2) return null;

        // 简单合并策略：使用第一个和最后一个的差异
        let firstItem = diffItems[0];
        let lastItem = diffItems[diffItems.length - 1];

        return {
            sequenceNbr: `merged_${firstItem.sequenceNbr}_${lastItem.sequenceNbr}`,
            item: lastItem.item, // 使用最后的差异
            channel: firstItem.channel,
            name: `合并操作 (${diffItems.length}项)`,
            params: lastItem.params,
            isDiff: true,
            diffStats: {
                changeCount: diffItems.reduce((sum, item) => sum + (item.diffStats?.changeCount || 0), 0),
                size: diffItems.reduce((sum, item) => sum + (item.diffStats?.size || 0), 0)
            },
            timestamp: lastItem.timestamp,
            mergedFrom: diffItems.map(item => item.sequenceNbr)
        };
    }

    /**
     * 获取撤销系统的完整状态报告
     */
    getSystemReport(constructId = null) {
        const report = {
            timestamp: new Date().toISOString(),
            config: RedoConfig.getAll(),
            performance: performanceMonitor.generateReport(constructId)
        };

        if (constructId) {
            const memoryStats = this.getMemoryStats(constructId);
            const warnings = performanceMonitor.getPerformanceWarnings(constructId);
            const recommendations = RedoConfig.getPerformanceRecommendations(memoryStats);

            report.project = {
                constructId,
                memoryStats,
                warnings,
                recommendations
            };
        } else {
            // 全局报告
            report.global = {
                totalProjects: this.redoQueueMap.size,
                warnings: performanceMonitor.getPerformanceWarnings(),
                totalMemoryUsage: this.getTotalMemoryUsage()
            };
        }

        return report;
    }

    /**
     * 获取所有项目的总内存使用量
     */
    getTotalMemoryUsage() {
        let totalSize = 0;
        for (const [constructId, _] of this.redoQueueMap) {
            const stats = this.getMemoryStats(constructId);
            totalSize += stats.totalSize;
        }
        return totalSize;
    }

    /**
     * 强制优化指定项目的撤销队列
     */
    forceOptimize(constructId) {
        return this.performOptimization(constructId);
    }

    /**
     * 设置撤销系统配置
     */
    setConfig(newConfig) {
        RedoConfig.setConfig(newConfig);

        if (RedoConfig.get('enableDebugLogging')) {
            console.log('撤销系统配置已更新:', newConfig);
        }
    }

    /**
     * 获取撤销系统配置
     */
    getConfig() {
        return RedoConfig.getAll();
    }

    /**
     * 重置性能监控数据
     */
    resetPerformanceData() {
        performanceMonitor.reset();
    }

    /**
     * 导出撤销数据（用于调试）
     */
    exportDebugData(constructId) {
        const queue = this.getRedoQueue(constructId);
        return {
            constructId,
            queueLength: queue.queue.length,
            currentPointer: queue.currentPointer,
            items: queue.queue.map(item => ({
                sequenceNbr: item.sequenceNbr,
                name: item.name,
                isDiff: item.isDiff,
                diffStats: item.diffStats,
                timestamp: item.timestamp,
                hasFullObject: !!item.fullObject
            })),
            memoryStats: this.getMemoryStats(constructId),
            performanceStats: performanceMonitor.getProjectStats(constructId)
        };
    }
    getRedoQueue(constructId) {
        if (!global.redoMap) {
            global.redoMap = new Map();
        }
        let queue =  global.redoMap.get(constructId);
        if(queue == null || queue == undefined){
            queue = this.createRedoQueue(constructId);
        }
        return queue;
    }

    createRedoQueue(constructId){
        if (!global.redoMap) {
            global.redoMap = new Map();
        }

        let initSize = 20;

        const freeMemory = os.freemem();
        let resultInt = freeMemory/1024/1024/100;
        
        if(resultInt <= 20){//2g
            initSize = 10;
        }
        if(resultInt <= 10){//1g
            initSize = 5;
        }
        //先固定大小 默认20
        let queue = new DynamicQueue(initSize);

        global.redoMap.set(constructId, queue);
        return queue;
    }
    /**
     * 恢复
     * @param {*} constructId
     * @param {*} queueId
     * @returns
     */
    redo(constructId,queueId){
        let queue = this.getRedoQueue(constructId);
        let queueObject = null;
        if(ObjectUtil.isEmpty(queueId)){
            queueObject = queue.dequeue(1);
        }else{
            queueObject = queue.dequeueById(queueId,1);
        }
        if(ObjectUtil.isNotEmpty(queueObject)){
            // 检查是否是最小化记录
            if (queueObject.isMinimal) {
                console.warn(`无法恢复最小化记录: ${queueObject.name} - ${queueObject.error}`);
                return {
                    error: 'Cannot restore minimal record',
                    message: queueObject.error,
                    sequenceNbr: queueObject.sequenceNbr,
                    channel: queueObject.channel,
                    name: queueObject.name
                };
            }

            let objectToRestore = null;

            if (queueObject.isDiff) {
                // 如果是差异记录，使用存储的重做状态
                objectToRestore = queueObject.redoState || queueObject.redoItem;
                if (!objectToRestore && queueObject.fullObject) {
                    objectToRestore = queueObject.fullObject;
                }
            } else {
                // 传统的完整对象记录
                objectToRestore = queueObject.redoItem;
            }

            if (objectToRestore) {
                try {
                    ObjectCacheUtils.updateObject(constructId, objectToRestore, queueObject.channel);
                    if (RedoConfig.get('enableDebugLogging')) {
                        console.log(`恢复操作完成 - ${queueObject.name}, 差异记录: ${queueObject.isDiff || false}`);
                    }
                } catch (error) {
                    console.error('更新对象失败:', error.message);
                    return {
                        error: 'Failed to update object',
                        message: error.message,
                        sequenceNbr: queueObject.sequenceNbr,
                        channel: queueObject.channel,
                        name: queueObject.name
                    };
                }
            } else {
                console.error('恢复失败：无法获取要恢复的对象状态');
                return {
                    error: 'No object to restore',
                    sequenceNbr: queueObject.sequenceNbr,
                    channel: queueObject.channel,
                    name: queueObject.name
                };
            }
        }else{
            return {};
        }
        return {sequenceNbr:queueObject.sequenceNbr,
            channel:queueObject.channel,
            name:queueObject.name,
            params: queueObject.params
        };
    }

    /**
     * 撤销
     * @param {*} constructId
     * @param {*} queueId
     * @returns
     */
    undo(constructId,queueId){
        let queue = this.getRedoQueue(constructId);
        let queueObject = null;
        if(ObjectUtil.isEmpty(queueId)){
            queueObject = queue.dequeue(0);
        }else{
            queueObject = queue.dequeueById(queueId,0);
        }
        if(ObjectUtil.isNotEmpty(queueObject)){
            let lastQueue = queue.getLastItem();//撤销时判断当前最新的一条是否保存了当前对象，用于恢复
            if(ObjectUtil.isEmpty(lastQueue.redoItem)){
                lastQueue.redoItem = ObjectCacheUtils.get(constructId,queueObject.channel);
            }

            let objectToRestore = null;

            if (queueObject.isDiff) {
                // 如果是差异记录，使用存储的操作前状态
                objectToRestore = queueObject.item; // item 现在存储的是操作前的状态
                if (RedoConfig.get('enableDebugLogging')) {
                    console.log(`撤销差异记录 - ${queueObject.name}`);
                }
            } else {
                // 传统的完整对象记录
                objectToRestore = queueObject.item;
                if (RedoConfig.get('enableDebugLogging')) {
                    console.log(`撤销完整对象记录 - ${queueObject.name}`);
                }
            }

            if (objectToRestore) {
                ObjectCacheUtils.updateObject(constructId, objectToRestore, queueObject.channel);
            } else {
                console.error('撤销失败：无法获取要恢复的对象状态');
                return {};
            }
        }else{
            return {};
        }
        return {sequenceNbr:queueObject.sequenceNbr,
            channel:queueObject.channel,
            name:queueObject.name,
            params: queueObject.params
        };
    }
    
    clearRedoList(params){
        if (!global.redoMap) {
            global.redoMap = new Map();
        }
        global.redoMap.delete(params.constructId);
    }
}

module.exports = RedoCacheUtils
  