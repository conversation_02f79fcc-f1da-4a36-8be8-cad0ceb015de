/**
 * 撤销回退系统配置管理
 */
class RedoConfig {
    
    static DEFAULT_CONFIG = {
        // 差异记录配置
        enableDiffMode: true,                    // 是否启用差异模式
        fullSnapshotInterval: 10,                // 每N个操作存储一次完整快照
        maxDiffSize: 50000,                      // 差异大小超过此值时存储完整快照(bytes)
        handleCircularReferences: true,          // 是否处理循环引用
        maxObjectDepth: 10,                      // 最大对象深度限制
        
        // 队列管理配置
        maxQueueSize: 100,                       // 最大队列长度
        maxRecordAge: 3600000,                   // 记录最大存活时间(ms) - 1小时
        
        // 优化配置
        enableAutoOptimization: true,            // 是否启用自动优化
        optimizationInterval: 50,                // 每N个操作执行一次优化
        smallDiffThreshold: 5,                   // 小差异的变更数量阈值
        
        // 内存监控配置
        enableMemoryMonitoring: true,            // 是否启用内存监控
        memoryWarningThreshold: 100 * 1024 * 1024, // 内存警告阈值(bytes) - 100MB
        memoryCleanupThreshold: 200 * 1024 * 1024, // 内存清理阈值(bytes) - 200MB
        
        // 调试配置
        enableDebugLogging: false,               // 是否启用调试日志
        enablePerformanceMonitoring: true       // 是否启用性能监控
    };

    static config = { ...RedoConfig.DEFAULT_CONFIG };

    /**
     * 获取配置项
     */
    static get(key) {
        return RedoConfig.config[key];
    }

    /**
     * 设置配置项
     */
    static set(key, value) {
        RedoConfig.config[key] = value;
    }

    /**
     * 批量设置配置
     */
    static setConfig(newConfig) {
        RedoConfig.config = { ...RedoConfig.config, ...newConfig };
    }

    /**
     * 重置为默认配置
     */
    static reset() {
        RedoConfig.config = { ...RedoConfig.DEFAULT_CONFIG };
    }

    /**
     * 获取完整配置
     */
    static getAll() {
        return { ...RedoConfig.config };
    }

    /**
     * 验证配置的有效性
     */
    static validate() {
        const errors = [];
        
        if (RedoConfig.config.fullSnapshotInterval < 1) {
            errors.push('fullSnapshotInterval must be >= 1');
        }
        
        if (RedoConfig.config.maxDiffSize < 1000) {
            errors.push('maxDiffSize must be >= 1000');
        }
        
        if (RedoConfig.config.maxQueueSize < 10) {
            errors.push('maxQueueSize must be >= 10');
        }
        
        if (RedoConfig.config.maxRecordAge < 60000) {
            errors.push('maxRecordAge must be >= 60000 (1 minute)');
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    /**
     * 根据系统性能动态调整配置
     */
    static autoTune(memoryUsage, queueLength, averageDiffSize) {
        const tuned = { ...RedoConfig.config };
        
        // 根据内存使用情况调整
        if (memoryUsage > RedoConfig.config.memoryCleanupThreshold) {
            tuned.fullSnapshotInterval = Math.max(5, tuned.fullSnapshotInterval - 2);
            tuned.maxQueueSize = Math.max(20, tuned.maxQueueSize - 10);
            tuned.maxRecordAge = Math.max(300000, tuned.maxRecordAge - 300000); // 减少5分钟
        } else if (memoryUsage < RedoConfig.config.memoryWarningThreshold / 2) {
            tuned.fullSnapshotInterval = Math.min(20, tuned.fullSnapshotInterval + 1);
            tuned.maxQueueSize = Math.min(200, tuned.maxQueueSize + 5);
        }
        
        // 根据队列长度调整
        if (queueLength > tuned.maxQueueSize * 0.8) {
            tuned.enableAutoOptimization = true;
            tuned.optimizationInterval = Math.max(10, tuned.optimizationInterval - 5);
        }
        
        // 根据平均差异大小调整
        if (averageDiffSize > tuned.maxDiffSize * 0.8) {
            tuned.maxDiffSize = Math.min(100000, tuned.maxDiffSize + 10000);
        }
        
        RedoConfig.config = tuned;
        
        if (RedoConfig.config.enableDebugLogging) {
            console.log('撤销系统配置已自动调优:', {
                memoryUsage: `${(memoryUsage / 1024 / 1024).toFixed(2)}MB`,
                queueLength,
                averageDiffSize: `${(averageDiffSize / 1024).toFixed(2)}KB`,
                newConfig: tuned
            });
        }
    }

    /**
     * 获取性能建议
     */
    static getPerformanceRecommendations(stats) {
        const recommendations = [];
        
        if (stats.compressionRatio < 50) {
            recommendations.push({
                type: 'warning',
                message: '差异压缩率较低，建议增加完整快照间隔',
                suggestion: 'fullSnapshotInterval += 2'
            });
        }
        
        if (stats.totalSize > RedoConfig.config.memoryWarningThreshold) {
            recommendations.push({
                type: 'critical',
                message: '撤销队列内存使用过高',
                suggestion: '启用自动清理或减少队列大小'
            });
        }
        
        if (stats.diffItems / stats.totalItems < 0.7) {
            recommendations.push({
                type: 'info',
                message: '差异记录比例较低，可以进一步优化',
                suggestion: '调整差异记录策略'
            });
        }
        
        return recommendations;
    }

    /**
     * 导出配置到JSON
     */
    static exportConfig() {
        return JSON.stringify(RedoConfig.config, null, 2);
    }

    /**
     * 从JSON导入配置
     */
    static importConfig(jsonString) {
        try {
            const importedConfig = JSON.parse(jsonString);
            const validation = RedoConfig.validate.call({ config: importedConfig });
            
            if (validation.isValid) {
                RedoConfig.config = { ...RedoConfig.DEFAULT_CONFIG, ...importedConfig };
                return { success: true };
            } else {
                return { success: false, errors: validation.errors };
            }
        } catch (error) {
            return { success: false, errors: ['Invalid JSON format'] };
        }
    }
}

module.exports = RedoConfig;
