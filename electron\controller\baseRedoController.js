const {Controller} = require("../../core");
const RedoCacheUtils = require('../../core/redo/RedoCacheUtils');
const {ResponseData} = require("../utils/ResponseData");

class BaseRedoController extends Controller{


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }

    getDoList(args){
        let {constructId} = args;
        let redoCache = new RedoCacheUtils();
        let queue = redoCache.getRedoQueue(constructId);
        return ResponseData.success({
            undo:queue.undoList(),redo:queue.redoList()
        });
    }
    /**
     * 恢复
     * @param {*} args 
     * @returns 
     */
    async redo(args){
        let {constructId,id} = args;
        let redoCache = new RedoCacheUtils();
        let item = redoCache.redo(constructId,id);
        return ResponseData.success(item);
    }
    /**
     * 撤销
     * @param arg
     * @return {Promise<void>}
     */
    async undo(args){
        let {constructId,id} = args;
        let redoCache = new RedoCacheUtils();
        let item = redoCache.undo(constructId,id);
        return ResponseData.success(item);
    }

    /**
     * 获取撤销系统状态报告
     * @param {*} args
     * @returns
     */
    async getSystemReport(args) {
        let {constructId} = args;
        let redoCache = new RedoCacheUtils();
        let report = redoCache.getSystemReport(constructId);
        return ResponseData.success(report);
    }

    /**
     * 获取内存使用统计
     * @param {*} args
     * @returns
     */
    async getMemoryStats(args) {
        let {constructId} = args;
        let redoCache = new RedoCacheUtils();
        let stats = redoCache.getMemoryStats(constructId);
        return ResponseData.success(stats);
    }

    /**
     * 强制优化撤销队列
     * @param {*} args
     * @returns
     */
    async forceOptimize(args) {
        let {constructId} = args;
        let redoCache = new RedoCacheUtils();
        let result = redoCache.forceOptimize(constructId);
        return ResponseData.success(result);
    }

    /**
     * 设置撤销系统配置
     * @param {*} args
     * @returns
     */
    async setConfig(args) {
        let {config} = args;
        let redoCache = new RedoCacheUtils();
        redoCache.setConfig(config);
        return ResponseData.success(true);
    }

    /**
     * 获取撤销系统配置
     * @param {*} args
     * @returns
     */
    async getConfig(args) {
        let redoCache = new RedoCacheUtils();
        let config = redoCache.getConfig();
        return ResponseData.success(config);
    }

    /**
     * 导出调试数据
     * @param {*} args
     * @returns
     */
    async exportDebugData(args) {
        let {constructId} = args;
        let redoCache = new RedoCacheUtils();
        let debugData = redoCache.exportDebugData(constructId);
        return ResponseData.success(debugData);
    }

}

BaseRedoController.toString = () => 'baseRedoController';
module.exports = BaseRedoController;
