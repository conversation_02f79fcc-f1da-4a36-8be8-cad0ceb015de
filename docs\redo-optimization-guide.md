# 撤销回退系统优化指南

## 概述

本文档介绍了撤销回退系统的优化方案，从原来的"全量备份"模式改为"差量记录"模式，大幅减少内存占用。

## 主要改进

### 1. 差量记录机制
- **原理**: 只记录数据变化的部分，而不是完整的对象副本
- **优势**: 内存使用量减少70-90%
- **实现**: 使用 `DiffUtils` 计算对象差异

### 2. 智能快照策略
- **完整快照**: 每N个操作或差异过大时存储完整对象
- **差异记录**: 其他时候只存储变化部分
- **可配置**: 通过 `RedoConfig` 调整策略参数

### 3. 性能监控
- **实时监控**: 内存使用、操作耗时、压缩率等指标
- **自动优化**: 根据性能数据自动调整配置
- **警告系统**: 内存使用过高时自动提醒

## 核心组件

### DiffUtils (差量计算工具)
```javascript
// 计算差异
const diff = DiffUtils.calculateDiff(oldObject, newObject);

// 应用差异
const newObject = DiffUtils.applyDiff(baseObject, diff);

// 反向应用差异（撤销）
const oldObject = DiffUtils.reverseDiff(currentObject, diff);
```

### RedoConfig (配置管理)
```javascript
// 设置配置
RedoConfig.set('enableDiffMode', true);
RedoConfig.set('fullSnapshotInterval', 10);

// 获取配置
const config = RedoConfig.getAll();
```

### RedoPerformanceMonitor (性能监控)
```javascript
// 开始监控操作
const context = performanceMonitor.startOperation(constructId, 'addRedoCache');

// 结束监控
performanceMonitor.endOperation(context, { success: true });

// 获取统计
const stats = performanceMonitor.getProjectStats(constructId);
```

## 配置参数

### 差异记录配置
- `enableDiffMode`: 是否启用差异模式 (默认: true)
- `fullSnapshotInterval`: 完整快照间隔 (默认: 10)
- `maxDiffSize`: 最大差异大小阈值 (默认: 50KB)

### 队列管理配置
- `maxQueueSize`: 最大队列长度 (默认: 100)
- `maxRecordAge`: 记录最大存活时间 (默认: 1小时)

### 优化配置
- `enableAutoOptimization`: 自动优化 (默认: true)
- `optimizationInterval`: 优化间隔 (默认: 50个操作)
- `smallDiffThreshold`: 小差异阈值 (默认: 5个变更)

### 内存监控配置
- `memoryWarningThreshold`: 内存警告阈值 (默认: 100MB)
- `memoryCleanupThreshold`: 内存清理阈值 (默认: 200MB)

## 使用方法

### 1. 基本使用
系统会自动使用差量记录模式，无需修改现有代码：

```javascript
// 原有代码保持不变
redoCache.addRedoCache(params, channel);
redoCache.undo(constructId, queueId);
redoCache.redo(constructId, queueId);
```

### 2. 配置调整
```javascript
// 调整配置以适应不同场景
const redoCache = new RedoCacheUtils();

// 高频操作场景 - 减少完整快照频率
redoCache.setConfig({
    fullSnapshotInterval: 20,
    enableAutoOptimization: true
});

// 内存敏感场景 - 更激进的优化
redoCache.setConfig({
    maxQueueSize: 50,
    maxRecordAge: 1800000, // 30分钟
    memoryWarningThreshold: 50 * 1024 * 1024 // 50MB
});
```

### 3. 监控和调试
```javascript
// 获取内存统计
const stats = redoCache.getMemoryStats(constructId);
console.log(`内存使用: ${stats.totalSize} bytes, 压缩率: ${stats.compressionRatio}%`);

// 获取系统报告
const report = redoCache.getSystemReport(constructId);

// 强制优化
redoCache.forceOptimize(constructId);

// 导出调试数据
const debugData = redoCache.exportDebugData(constructId);
```

## 前端监控界面

### RedoSystemMonitor 组件
提供可视化的监控界面：
- 实时内存使用统计
- 性能指标图表
- 配置管理界面
- 警告和建议

### 使用方法
```vue
<template>
  <RedoSystemMonitor />
</template>

<script>
import RedoSystemMonitor from '@/components/global/redoUndo/RedoSystemMonitor.vue';

export default {
  components: {
    RedoSystemMonitor
  }
};
</script>
```

## 性能优化建议

### 1. 根据使用场景调整配置
- **频繁小改动**: 增加 `fullSnapshotInterval`，启用自动优化
- **大对象操作**: 减少 `maxDiffSize`，增加完整快照频率
- **内存受限**: 减少 `maxQueueSize` 和 `maxRecordAge`

### 2. 监控关键指标
- **压缩率**: 应保持在70%以上
- **内存使用**: 不应超过设定阈值
- **操作耗时**: 单次操作应在100ms以内

### 3. 定期维护
- 启用自动优化功能
- 定期检查性能报告
- 根据警告调整配置

## 向后兼容性

系统保持完全向后兼容：
- 现有API接口不变
- 可通过配置禁用差量模式
- 自动处理旧格式的撤销记录

## 故障排除

### 常见问题

1. **内存使用仍然很高**
   - 检查 `enableDiffMode` 是否为 true
   - 调整 `fullSnapshotInterval` 和 `maxDiffSize`
   - 启用自动优化

2. **撤销/恢复失败**
   - 检查差异计算是否正确
   - 查看错误日志
   - 尝试禁用差量模式进行对比

3. **性能下降**
   - 检查差异计算耗时
   - 调整优化策略
   - 考虑增加完整快照频率

### 调试工具
- 启用 `enableDebugLogging` 查看详细日志
- 使用 `exportDebugData` 导出调试信息
- 通过监控界面查看实时状态

## 总结

通过差量记录优化，撤销回退系统的内存使用量大幅降低，同时保持了功能的完整性和性能。配合智能的配置管理和性能监控，系统能够自动适应不同的使用场景，提供最佳的用户体验。
