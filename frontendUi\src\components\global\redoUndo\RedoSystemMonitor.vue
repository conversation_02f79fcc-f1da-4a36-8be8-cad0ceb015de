<!--
 * @Description: 撤销回退系统监控组件
 * @Author: AI Assistant
 * @Date: 2025-01-16
-->
<template>
  <div class="redo-system-monitor">
    <a-card title="撤销系统监控" size="small">
      <!-- 系统状态概览 -->
      <div class="status-overview">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic
              title="队列长度"
              :value="stats?.totalItems || 0"
              :value-style="{ color: getStatusColor('queue') }"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="内存使用"
              :value="formatMemorySize(stats?.totalSize || 0)"
              :value-style="{ color: getStatusColor('memory') }"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="压缩率"
              :value="stats?.compressionRatio || 0"
              suffix="%"
              :value-style="{ color: getStatusColor('compression') }"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="差异记录"
              :value="stats?.diffItems || 0"
              :suffix="`/${stats?.totalItems || 0}`"
            />
          </a-col>
        </a-row>
      </div>

      <!-- 操作按钮 -->
      <div class="actions" style="margin: 16px 0;">
        <a-space>
          <a-button @click="refreshStats" :loading="loading">
            <ReloadOutlined /> 刷新
          </a-button>
          <a-button @click="forceOptimize" :loading="optimizing">
            <ThunderboltOutlined /> 强制优化
          </a-button>
          <a-button @click="showConfig = true">
            <SettingOutlined /> 配置
          </a-button>
          <a-button @click="exportDebugData">
            <DownloadOutlined /> 导出调试数据
          </a-button>
        </a-space>
      </div>

      <!-- 警告信息 -->
      <div v-if="warnings.length > 0" class="warnings">
        <a-alert
          v-for="warning in warnings"
          :key="warning.message"
          :message="warning.message"
          :type="getWarningType(warning.type)"
          show-icon
          closable
          style="margin-bottom: 8px;"
        />
      </div>

      <!-- 性能图表 -->
      <div class="performance-chart" style="margin-top: 16px;">
        <a-tabs>
          <a-tab-pane key="memory" tab="内存使用趋势">
            <div ref="memoryChart" style="height: 200px;"></div>
          </a-tab-pane>
          <a-tab-pane key="operations" tab="操作性能">
            <div ref="operationChart" style="height: 200px;"></div>
          </a-tab-pane>
        </a-tabs>
      </div>

      <!-- 详细统计 -->
      <a-collapse style="margin-top: 16px;">
        <a-collapse-panel key="details" header="详细统计">
          <a-descriptions :column="2" size="small">
            <a-descriptions-item label="差异记录数">
              {{ stats?.diffItems || 0 }}
            </a-descriptions-item>
            <a-descriptions-item label="完整记录数">
              {{ stats?.fullItems || 0 }}
            </a-descriptions-item>
            <a-descriptions-item label="差异数据大小">
              {{ formatMemorySize(stats?.diffSize || 0) }}
            </a-descriptions-item>
            <a-descriptions-item label="完整数据大小">
              {{ formatMemorySize(stats?.fullSize || 0) }}
            </a-descriptions-item>
          </a-descriptions>
        </a-collapse-panel>
      </a-collapse>
    </a-card>

    <!-- 配置弹窗 -->
    <a-modal
      v-model:visible="showConfig"
      title="撤销系统配置"
      width="600px"
      @ok="saveConfig"
    >
      <a-form :model="config" layout="vertical">
        <a-form-item label="启用差异模式">
          <a-switch v-model:checked="config.enableDiffMode" />
        </a-form-item>
        <a-form-item label="完整快照间隔">
          <a-input-number
            v-model:value="config.fullSnapshotInterval"
            :min="1"
            :max="50"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item label="最大差异大小 (bytes)">
          <a-input-number
            v-model:value="config.maxDiffSize"
            :min="1000"
            :max="1000000"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item label="最大队列长度">
          <a-input-number
            v-model:value="config.maxQueueSize"
            :min="10"
            :max="500"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item label="启用自动优化">
          <a-switch v-model:checked="config.enableAutoOptimization" />
        </a-form-item>
        <a-form-item label="启用调试日志">
          <a-switch v-model:checked="config.enableDebugLogging" />
        </a-form-item>
        <a-form-item label="处理循环引用">
          <a-switch v-model:checked="config.handleCircularReferences" />
        </a-form-item>
        <a-form-item label="最大对象深度">
          <a-input-number
            v-model:value="config.maxObjectDepth"
            :min="5"
            :max="20"
            style="width: 100%"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted } from 'vue';
import { message } from 'ant-design-vue';
import {
  ReloadOutlined,
  ThunderboltOutlined,
  SettingOutlined,
  DownloadOutlined
} from '@ant-design/icons-vue';
import { ipc } from '@/utils/ipcRenderer';

export default {
  name: 'RedoSystemMonitor',
  components: {
    ReloadOutlined,
    ThunderboltOutlined,
    SettingOutlined,
    DownloadOutlined
  },
  setup() {
    const stats = ref(null);
    const warnings = ref([]);
    const loading = ref(false);
    const optimizing = ref(false);
    const showConfig = ref(false);
    const config = reactive({});
    
    let refreshTimer = null;

    // 获取当前项目ID
    const getConstructId = () => {
      const url = new URL(window.location.href);
      return url.searchParams.get('constructSequenceNbr');
    };

    // 刷新统计数据
    const refreshStats = async () => {
      loading.value = true;
      try {
        const constructId = getConstructId();
        const [statsRes, reportRes] = await Promise.all([
          ipc.invoke('controller.baseRedoController.getMemoryStats', { constructId }),
          ipc.invoke('controller.baseRedoController.getSystemReport', { constructId })
        ]);

        if (statsRes.status === 200) {
          stats.value = statsRes.result;
        }

        if (reportRes.status === 200) {
          warnings.value = reportRes.result.project?.warnings || [];
        }
      } catch (error) {
        console.error('获取撤销系统统计失败:', error);
        message.error('获取统计数据失败');
      } finally {
        loading.value = false;
      }
    };

    // 强制优化
    const forceOptimize = async () => {
      optimizing.value = true;
      try {
        const constructId = getConstructId();
        const res = await ipc.invoke('controller.baseRedoController.forceOptimize', { constructId });
        
        if (res.status === 200) {
          message.success('队列优化完成');
          await refreshStats();
        } else {
          message.error('优化失败');
        }
      } catch (error) {
        console.error('强制优化失败:', error);
        message.error('优化失败');
      } finally {
        optimizing.value = false;
      }
    };

    // 加载配置
    const loadConfig = async () => {
      try {
        const res = await ipc.invoke('controller.baseRedoController.getConfig', {});
        if (res.status === 200) {
          Object.assign(config, res.result);
        }
      } catch (error) {
        console.error('加载配置失败:', error);
      }
    };

    // 保存配置
    const saveConfig = async () => {
      try {
        const res = await ipc.invoke('controller.baseRedoController.setConfig', { config });
        if (res.status === 200) {
          message.success('配置保存成功');
          showConfig.value = false;
          await refreshStats();
        } else {
          message.error('配置保存失败');
        }
      } catch (error) {
        console.error('保存配置失败:', error);
        message.error('配置保存失败');
      }
    };

    // 导出调试数据
    const exportDebugData = async () => {
      try {
        const constructId = getConstructId();
        const res = await ipc.invoke('controller.baseRedoController.exportDebugData', { constructId });
        
        if (res.status === 200) {
          const dataStr = JSON.stringify(res.result, null, 2);
          const blob = new Blob([dataStr], { type: 'application/json' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `redo-debug-${constructId}-${Date.now()}.json`;
          a.click();
          URL.revokeObjectURL(url);
          message.success('调试数据已导出');
        }
      } catch (error) {
        console.error('导出调试数据失败:', error);
        message.error('导出失败');
      }
    };

    // 格式化内存大小
    const formatMemorySize = (bytes) => {
      if (bytes < 1024) return `${bytes}B`;
      if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)}KB`;
      return `${(bytes / 1024 / 1024).toFixed(1)}MB`;
    };

    // 获取状态颜色
    const getStatusColor = (type) => {
      if (!stats.value) return '#666';
      
      switch (type) {
        case 'queue':
          return stats.value.totalItems > 80 ? '#ff4d4f' : '#52c41a';
        case 'memory':
          return stats.value.totalSize > 50 * 1024 * 1024 ? '#ff4d4f' : '#52c41a';
        case 'compression':
          return stats.value.compressionRatio > 70 ? '#52c41a' : '#faad14';
        default:
          return '#666';
      }
    };

    // 获取警告类型
    const getWarningType = (type) => {
      switch (type) {
        case 'critical': return 'error';
        case 'warning': return 'warning';
        case 'info': return 'info';
        default: return 'warning';
      }
    };

    onMounted(async () => {
      await loadConfig();
      await refreshStats();
      
      // 定时刷新
      refreshTimer = setInterval(refreshStats, 30000); // 30秒刷新一次
    });

    onUnmounted(() => {
      if (refreshTimer) {
        clearInterval(refreshTimer);
      }
    });

    return {
      stats,
      warnings,
      loading,
      optimizing,
      showConfig,
      config,
      refreshStats,
      forceOptimize,
      saveConfig,
      exportDebugData,
      formatMemorySize,
      getStatusColor,
      getWarningType
    };
  }
};
</script>

<style scoped>
.redo-system-monitor {
  padding: 16px;
}

.status-overview {
  margin-bottom: 16px;
}

.warnings {
  margin: 16px 0;
}

.actions {
  text-align: center;
}
</style>
