const {ObjectUtil} = require('../../common/ObjectUtil');

/**
 * 差量计算工具类
 * 用于计算对象之间的差异，支持撤销回退的差量记录
 */
class DiffUtils {

    /**
     * 计算两个对象之间的差异
     * @param {Object} oldObj 旧对象
     * @param {Object} newObj 新对象
     * @param {String} path 当前路径，用于调试
     * @returns {Object} 差异对象，包含变更的字段路径和值
     */
    static calculateDiff(oldObj, newObj, path = '') {
        const changes = {};
        
        // 处理基本类型
        if (oldObj === newObj) {
            return null;
        }
        
        if (oldObj === null || newObj === null || 
            typeof oldObj !== 'object' || typeof newObj !== 'object') {
            return {
                type: 'value',
                oldValue: oldObj,
                newValue: newObj,
                path: path
            };
        }

        // 处理数组
        if (Array.isArray(oldObj) && Array.isArray(newObj)) {
            return this.calculateArrayDiff(oldObj, newObj, path);
        }

        // 处理对象
        if (Array.isArray(oldObj) || Array.isArray(newObj)) {
            return {
                type: 'type_change',
                oldValue: oldObj,
                newValue: newObj,
                path: path
            };
        }

        return this.calculateObjectDiff(oldObj, newObj, path);
    }

    /**
     * 计算数组差异
     */
    static calculateArrayDiff(oldArr, newArr, path) {
        const changes = {};
        let hasChanges = false;

        // 检查长度变化
        if (oldArr.length !== newArr.length) {
            changes.length = {
                type: 'length_change',
                oldValue: oldArr.length,
                newValue: newArr.length,
                path: `${path}.length`
            };
            hasChanges = true;
        }

        // 检查每个元素
        const maxLength = Math.max(oldArr.length, newArr.length);
        for (let i = 0; i < maxLength; i++) {
            const oldItem = i < oldArr.length ? oldArr[i] : undefined;
            const newItem = i < newArr.length ? newArr[i] : undefined;
            
            const itemDiff = this.calculateDiff(oldItem, newItem, `${path}[${i}]`);
            if (itemDiff) {
                changes[i] = itemDiff;
                hasChanges = true;
            }
        }

        return hasChanges ? {
            type: 'array',
            changes: changes,
            path: path
        } : null;
    }

    /**
     * 计算对象差异
     */
    static calculateObjectDiff(oldObj, newObj, path) {
        const changes = {};
        let hasChanges = false;

        // 获取所有键
        const allKeys = new Set([...Object.keys(oldObj), ...Object.keys(newObj)]);

        for (const key of allKeys) {
            const oldValue = oldObj[key];
            const newValue = newObj[key];
            const currentPath = path ? `${path}.${key}` : key;

            const diff = this.calculateDiff(oldValue, newValue, currentPath);
            if (diff) {
                changes[key] = diff;
                hasChanges = true;
            }
        }

        return hasChanges ? {
            type: 'object',
            changes: changes,
            path: path
        } : null;
    }

    /**
     * 应用差异到对象
     * @param {Object} targetObj 目标对象
     * @param {Object} diff 差异对象
     * @returns {Object} 应用差异后的对象
     */
    static applyDiff(targetObj, diff) {
        if (!diff) return targetObj;

        // 深拷贝目标对象以避免修改原对象
        const result = ObjectUtil.cloneDeep(targetObj);
        
        this.applyDiffToObject(result, diff);
        return result;
    }

    /**
     * 将差异应用到对象（内部方法）
     */
    static applyDiffToObject(obj, diff) {
        if (!diff || !diff.type) return;

        switch (diff.type) {
            case 'value':
                // 这种情况需要在上层处理，因为需要替换整个值
                break;
            case 'object':
                this.applyObjectDiff(obj, diff.changes);
                break;
            case 'array':
                this.applyArrayDiff(obj, diff.changes);
                break;
            case 'type_change':
                // 类型变化需要在上层处理
                break;
        }
    }

    /**
     * 应用对象差异
     */
    static applyObjectDiff(obj, changes) {
        for (const [key, change] of Object.entries(changes)) {
            if (change.type === 'value') {
                obj[key] = change.newValue;
            } else {
                if (!obj[key]) {
                    obj[key] = change.type === 'array' ? [] : {};
                }
                this.applyDiffToObject(obj[key], change);
            }
        }
    }

    /**
     * 应用数组差异
     */
    static applyArrayDiff(arr, changes) {
        // 先处理长度变化
        if (changes.length) {
            arr.length = changes.length.newValue;
        }

        // 处理元素变化
        for (const [index, change] of Object.entries(changes)) {
            if (index === 'length') continue;
            
            const idx = parseInt(index);
            if (change.type === 'value') {
                arr[idx] = change.newValue;
            } else {
                if (!arr[idx]) {
                    arr[idx] = change.type === 'array' ? [] : {};
                }
                this.applyDiffToObject(arr[idx], change);
            }
        }
    }

    /**
     * 反向应用差异（用于撤销）
     * @param {Object} targetObj 目标对象
     * @param {Object} diff 差异对象
     * @returns {Object} 撤销差异后的对象
     */
    static reverseDiff(targetObj, diff) {
        if (!diff) return targetObj;

        const result = ObjectUtil.cloneDeep(targetObj);
        this.reverseDiffToObject(result, diff);
        return result;
    }

    /**
     * 反向应用差异到对象
     */
    static reverseDiffToObject(obj, diff) {
        if (!diff || !diff.type) return;

        switch (diff.type) {
            case 'value':
                // 需要在上层处理
                break;
            case 'object':
                this.reverseObjectDiff(obj, diff.changes);
                break;
            case 'array':
                this.reverseArrayDiff(obj, diff.changes);
                break;
        }
    }

    /**
     * 反向应用对象差异
     */
    static reverseObjectDiff(obj, changes) {
        for (const [key, change] of Object.entries(changes)) {
            if (change.type === 'value') {
                if (change.oldValue === undefined) {
                    delete obj[key];
                } else {
                    obj[key] = change.oldValue;
                }
            } else {
                if (obj[key]) {
                    this.reverseDiffToObject(obj[key], change);
                }
            }
        }
    }

    /**
     * 反向应用数组差异
     */
    static reverseArrayDiff(arr, changes) {
        // 处理元素变化
        for (const [index, change] of Object.entries(changes)) {
            if (index === 'length') continue;
            
            const idx = parseInt(index);
            if (change.type === 'value') {
                if (change.oldValue === undefined) {
                    arr.splice(idx, 1);
                } else {
                    arr[idx] = change.oldValue;
                }
            } else {
                if (arr[idx]) {
                    this.reverseDiffToObject(arr[idx], change);
                }
            }
        }

        // 最后处理长度变化
        if (changes.length) {
            arr.length = changes.length.oldValue;
        }
    }

    /**
     * 压缩差异对象，移除不必要的嵌套
     */
    static compressDiff(diff) {
        if (!diff || !diff.changes) return diff;

        const compressed = { ...diff };
        compressed.changes = {};

        for (const [key, change] of Object.entries(diff.changes)) {
            if (change.type === 'value' && change.oldValue === change.newValue) {
                // 跳过没有实际变化的项
                continue;
            }
            compressed.changes[key] = this.compressDiff(change);
        }

        return Object.keys(compressed.changes).length > 0 ? compressed : null;
    }

    /**
     * 获取差异的统计信息
     */
    static getDiffStats(diff) {
        if (!diff) return { changeCount: 0, size: 0 };

        let changeCount = 0;
        let size = JSON.stringify(diff).length;

        const countChanges = (d) => {
            if (!d) return;
            
            if (d.type === 'value') {
                changeCount++;
            } else if (d.changes) {
                for (const change of Object.values(d.changes)) {
                    countChanges(change);
                }
            }
        };

        countChanges(diff);
        return { changeCount, size };
    }
}

module.exports = DiffUtils;
