const {ObjectUtil} = require('../../common/ObjectUtil');

/**
 * 差量计算工具类
 * 用于计算对象之间的差异，支持撤销回退的差量记录
 */
class DiffUtils {

    /**
     * 计算两个对象之间的差异
     * @param {Object} oldObj 旧对象
     * @param {Object} newObj 新对象
     * @param {String} path 当前路径，用于调试
     * @param {WeakSet} visited 已访问的对象集合，用于检测循环引用
     * @returns {Object} 差异对象，包含变更的字段路径和值
     */
    static calculateDiff(oldObj, newObj, path = '', visited = new WeakSet()) {
        // 处理基本类型
        if (oldObj === newObj) {
            return null;
        }

        if (oldObj === null || newObj === null ||
            typeof oldObj !== 'object' || typeof newObj !== 'object') {
            return {
                type: 'value',
                oldValue: this.serializeSafeValue(oldObj),
                newValue: this.serializeSafeValue(newObj),
                path: path
            };
        }

        // 检测循环引用
        if (visited.has(oldObj) || visited.has(newObj)) {
            return {
                type: 'circular_reference',
                path: path,
                message: 'Circular reference detected'
            };
        }

        // 添加到已访问集合
        visited.add(oldObj);
        visited.add(newObj);

        try {
            // 处理数组
            if (Array.isArray(oldObj) && Array.isArray(newObj)) {
                return this.calculateArrayDiff(oldObj, newObj, path, visited);
            }

            // 处理对象
            if (Array.isArray(oldObj) || Array.isArray(newObj)) {
                return {
                    type: 'type_change',
                    oldValue: this.serializeSafeValue(oldObj),
                    newValue: this.serializeSafeValue(newObj),
                    path: path
                };
            }

            return this.calculateObjectDiff(oldObj, newObj, path, visited);
        } finally {
            // 清理已访问集合（对于当前分支）
            visited.delete(oldObj);
            visited.delete(newObj);
        }
    }

    /**
     * 安全序列化值，处理循环引用和特殊对象
     */
    static serializeSafeValue(value) {
        if (value === null || value === undefined) {
            return value;
        }

        if (typeof value !== 'object') {
            return value;
        }

        // 处理特殊对象类型
        if (value instanceof Date) {
            return { __type: 'Date', value: value.toISOString() };
        }

        if (value instanceof RegExp) {
            return { __type: 'RegExp', source: value.source, flags: value.flags };
        }

        // 对于复杂对象，只保存关键标识信息
        const safeObj = {};

        // 特殊处理TreeNode类型
        if (value.constructor?.name === 'TreeNode') {
            return this.serializeTreeNode(value);
        }

        // 尝试获取对象的标识字段
        const identifierFields = ['id', 'sequenceNbr', 'name', 'code', 'key', 'type', 'kind'];
        for (const field of identifierFields) {
            if (value[field] !== undefined && typeof value[field] !== 'object') {
                safeObj[field] = value[field];
            }
        }

        // 如果没有找到标识字段，记录对象类型
        if (Object.keys(safeObj).length === 0) {
            safeObj.__type = value.constructor?.name || 'Object';
            safeObj.__hasCircularRef = true;
        }

        return safeObj;
    }

    /**
     * 专门处理TreeNode类型的序列化
     */
    static serializeTreeNode(treeNode) {
        const safeNode = {
            __type: 'TreeNode'
        };

        // 保存TreeNode的关键属性，但排除循环引用的字段
        const safeFields = [
            'sequenceNbr', 'id', 'name', 'code', 'type', 'kind',
            'level', 'index', 'isLeaf', 'expanded', 'selected'
        ];

        for (const field of safeFields) {
            if (treeNode[field] !== undefined && typeof treeNode[field] !== 'object') {
                safeNode[field] = treeNode[field];
            }
        }

        // 记录子节点数量而不是具体内容
        if (treeNode.children && Array.isArray(treeNode.children)) {
            safeNode.childrenCount = treeNode.children.length;
        }

        // 记录父节点的标识而不是整个对象
        if (treeNode.parent && treeNode.parent.sequenceNbr) {
            safeNode.parentId = treeNode.parent.sequenceNbr;
        }

        return safeNode;
    }

    /**
     * 计算数组差异
     */
    static calculateArrayDiff(oldArr, newArr, path, visited = new WeakSet()) {
        const changes = {};
        let hasChanges = false;

        // 检查长度变化
        if (oldArr.length !== newArr.length) {
            changes.length = {
                type: 'length_change',
                oldValue: oldArr.length,
                newValue: newArr.length,
                path: `${path}.length`
            };
            hasChanges = true;
        }

        // 检查每个元素，限制深度以避免过深的递归
        const maxLength = Math.min(Math.max(oldArr.length, newArr.length), 1000); // 限制最大长度
        for (let i = 0; i < maxLength; i++) {
            const oldItem = i < oldArr.length ? oldArr[i] : undefined;
            const newItem = i < newArr.length ? newArr[i] : undefined;

            const itemDiff = this.calculateDiff(oldItem, newItem, `${path}[${i}]`, visited);
            if (itemDiff) {
                changes[i] = itemDiff;
                hasChanges = true;
            }
        }

        return hasChanges ? {
            type: 'array',
            changes: changes,
            path: path
        } : null;
    }

    /**
     * 计算对象差异
     */
    static calculateObjectDiff(oldObj, newObj, path, visited = new WeakSet()) {
        const changes = {};
        let hasChanges = false;

        // 获取所有键，但排除可能导致循环引用的字段
        const excludeFields = ['parent', 'children', '__proto__', 'constructor'];
        const oldKeys = Object.keys(oldObj).filter(key => !excludeFields.includes(key));
        const newKeys = Object.keys(newObj).filter(key => !excludeFields.includes(key));
        const allKeys = new Set([...oldKeys, ...newKeys]);

        // 限制处理的字段数量，避免过大的对象
        const keyArray = Array.from(allKeys).slice(0, 100); // 最多处理100个字段

        for (const key of keyArray) {
            const oldValue = oldObj[key];
            const newValue = newObj[key];
            const currentPath = path ? `${path}.${key}` : key;

            // 跳过函数类型的属性
            if (typeof oldValue === 'function' || typeof newValue === 'function') {
                continue;
            }

            const diff = this.calculateDiff(oldValue, newValue, currentPath, visited);
            if (diff) {
                changes[key] = diff;
                hasChanges = true;
            }
        }

        return hasChanges ? {
            type: 'object',
            changes: changes,
            path: path
        } : null;
    }

    /**
     * 应用差异到对象
     * @param {Object} targetObj 目标对象
     * @param {Object} diff 差异对象
     * @returns {Object} 应用差异后的对象
     */
    static applyDiff(targetObj, diff) {
        if (!diff) return targetObj;

        // 深拷贝目标对象以避免修改原对象
        const result = ObjectUtil.cloneDeep(targetObj);
        
        this.applyDiffToObject(result, diff);
        return result;
    }

    /**
     * 将差异应用到对象（内部方法）
     */
    static applyDiffToObject(obj, diff) {
        if (!diff || !diff.type) return;

        switch (diff.type) {
            case 'value':
                // 这种情况需要在上层处理，因为需要替换整个值
                break;
            case 'object':
                this.applyObjectDiff(obj, diff.changes);
                break;
            case 'array':
                this.applyArrayDiff(obj, diff.changes);
                break;
            case 'type_change':
                // 类型变化需要在上层处理
                break;
        }
    }

    /**
     * 应用对象差异
     */
    static applyObjectDiff(obj, changes) {
        for (const [key, change] of Object.entries(changes)) {
            if (change.type === 'value') {
                obj[key] = change.newValue;
            } else {
                if (!obj[key]) {
                    obj[key] = change.type === 'array' ? [] : {};
                }
                this.applyDiffToObject(obj[key], change);
            }
        }
    }

    /**
     * 应用数组差异
     */
    static applyArrayDiff(arr, changes) {
        // 先处理长度变化
        if (changes.length) {
            arr.length = changes.length.newValue;
        }

        // 处理元素变化
        for (const [index, change] of Object.entries(changes)) {
            if (index === 'length') continue;
            
            const idx = parseInt(index);
            if (change.type === 'value') {
                arr[idx] = change.newValue;
            } else {
                if (!arr[idx]) {
                    arr[idx] = change.type === 'array' ? [] : {};
                }
                this.applyDiffToObject(arr[idx], change);
            }
        }
    }

    /**
     * 反向应用差异（用于撤销）
     * @param {Object} targetObj 目标对象
     * @param {Object} diff 差异对象
     * @returns {Object} 撤销差异后的对象
     */
    static reverseDiff(targetObj, diff) {
        if (!diff) return targetObj;

        const result = ObjectUtil.cloneDeep(targetObj);
        this.reverseDiffToObject(result, diff);
        return result;
    }

    /**
     * 反向应用差异到对象
     */
    static reverseDiffToObject(obj, diff) {
        if (!diff || !diff.type) return;

        switch (diff.type) {
            case 'value':
                // 需要在上层处理
                break;
            case 'object':
                this.reverseObjectDiff(obj, diff.changes);
                break;
            case 'array':
                this.reverseArrayDiff(obj, diff.changes);
                break;
        }
    }

    /**
     * 反向应用对象差异
     */
    static reverseObjectDiff(obj, changes) {
        for (const [key, change] of Object.entries(changes)) {
            if (change.type === 'value') {
                if (change.oldValue === undefined) {
                    delete obj[key];
                } else {
                    obj[key] = change.oldValue;
                }
            } else {
                if (obj[key]) {
                    this.reverseDiffToObject(obj[key], change);
                }
            }
        }
    }

    /**
     * 反向应用数组差异
     */
    static reverseArrayDiff(arr, changes) {
        // 处理元素变化
        for (const [index, change] of Object.entries(changes)) {
            if (index === 'length') continue;
            
            const idx = parseInt(index);
            if (change.type === 'value') {
                if (change.oldValue === undefined) {
                    arr.splice(idx, 1);
                } else {
                    arr[idx] = change.oldValue;
                }
            } else {
                if (arr[idx]) {
                    this.reverseDiffToObject(arr[idx], change);
                }
            }
        }

        // 最后处理长度变化
        if (changes.length) {
            arr.length = changes.length.oldValue;
        }
    }

    /**
     * 压缩差异对象，移除不必要的嵌套
     */
    static compressDiff(diff) {
        if (!diff || !diff.changes) return diff;

        const compressed = { ...diff };
        compressed.changes = {};

        for (const [key, change] of Object.entries(diff.changes)) {
            if (change.type === 'value' && change.oldValue === change.newValue) {
                // 跳过没有实际变化的项
                continue;
            }
            compressed.changes[key] = this.compressDiff(change);
        }

        return Object.keys(compressed.changes).length > 0 ? compressed : null;
    }

    /**
     * 获取差异的统计信息
     */
    static getDiffStats(diff) {
        if (!diff) return { changeCount: 0, size: 0 };

        let changeCount = 0;
        let size = 0;

        try {
            // 使用安全的JSON序列化
            size = this.safeStringify(diff).length;
        } catch (error) {
            console.warn('计算差异大小时出错:', error.message);
            size = 0;
        }

        const countChanges = (d, visited = new WeakSet()) => {
            if (!d || typeof d !== 'object') return;

            // 防止循环引用
            if (visited.has(d)) return;
            visited.add(d);

            if (d.type === 'value' || d.type === 'circular_reference') {
                changeCount++;
            } else if (d.changes) {
                for (const change of Object.values(d.changes)) {
                    countChanges(change, visited);
                }
            }
        };

        countChanges(diff);
        return { changeCount, size };
    }

    /**
     * 安全的JSON序列化，处理循环引用
     */
    static safeStringify(obj, space = 0) {
        const seen = new WeakSet();

        return JSON.stringify(obj, (key, value) => {
            if (typeof value === 'object' && value !== null) {
                if (seen.has(value)) {
                    return '[Circular Reference]';
                }
                seen.add(value);
            }
            return value;
        }, space);
    }
}

module.exports = DiffUtils;
